package com.trinasolar.integration.service.appmarket.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.integration.api.ApiMarketProvider;
import com.trinasolar.integration.api.ApproveProvider;
import com.trinasolar.integration.api.UpmsProvider;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.integration.api.entity.ApiPublishRecord;
import com.trinasolar.integration.api.entity.ApiSubscribeRecord;
import com.trinasolar.integration.api.enums.ApiRollbackStatus;
import com.trinasolar.integration.api.enums.SubscribeStatusEnum;
import com.trinasolar.integration.config.security.aes.AESConfig;
import com.trinasolar.integration.dao.ApiPublishRecordMapper;
import com.trinasolar.integration.dao.ApiSubscribeRecordMapper;
import com.trinasolar.integration.dto.apimarket.ApiDetailInfo;
import com.trinasolar.integration.dto.apimarket.UserDTO;
import com.trinasolar.integration.execption.ApiException;
import com.trinasolar.integration.service.appmarket.ApiMarketService;
import com.trinasolar.integration.util.DateUtil;
import com.trinasolar.integration.util.RequestUtils;
import com.trinasolar.tasc.framework.common.exception.ApiMarketException;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriTemplate;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ApiMarketServiceImpl implements ApiMarketService {

    @Autowired
    private AESConfig aesConfig;

    @Autowired
    private UpmsProvider upmsProvider;

    @Autowired
    private ApiMarketProvider apiMarketProvider;

    @Autowired
    private ApiSubscribeRecordMapper apiSubscribeRecordMapper;

    @Autowired
    private ApiPublishRecordMapper apiPublishRecordMapper;

    @Autowired
    private ApproveProvider approveProvider;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String createToken() {
        User currentUser = RequestUtils.getCurrentUser();
        if (currentUser == null) {
            throw new ApiMarketException("当前用户请求为空");
        }
        UserDTO user = new UserDTO();
        try {
            Long id = Long.parseLong(currentUser.getId());
            user.setUsername(currentUser.getUsername());
            user.setUserRealname(currentUser.getUserRealname());
            user.setId(id);
            String userCode = upmsProvider.getByIds(List.of(id)).getData().get(0).getUserCode();
            user.setUserCode(userCode);
        } catch (Exception e) {
            log.error("当前用户信息处理失败", e);
            throw new ApiMarketException("当前用户信息处理失败,生成API-token异常!");
        }
        return aesConfig.encrypt(JacksonUtils.toJson(user));
    }

    @Override
    public UserDTO checkToken(String token) {
        try {
            String decrypt = aesConfig.decrypt(token);
            UserDTO userDTO = JSONObject.parseObject(decrypt, UserDTO.class);
            String createdTime = userDTO.getCreatedTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime createDateTime = LocalDateTime.parse(createdTime, formatter);
            long diffMillis = Duration.between(createDateTime, LocalDateTime.now()).toMillis();
            // 校验是否超过5分钟（300000毫秒）
            if (diffMillis > 300000) {
                throw new ApiMarketException("当前时间超过创建时间5分钟，操作无效");
            }
            return userDTO;
        } catch (Exception e) {
            log.error("API token校验失败", e);
            throw new ApiMarketException("API token校验失败！");
        }
    }

    @Override
    public PageResultDTO getApiCardList(String name, String apiPath, String pubOrgName,
                                        String startTime, String endTime, String desc,
                                        Integer pageSize, Integer pageNo, Integer tagId) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            startTime = null;
            endTime = null;
        }
        CommonResult<PageResultDTO> result = apiMarketProvider.getApiCardList(createToken(), name, apiPath, pubOrgName,
                startTime, endTime, desc, pageSize, pageNo, tagId);
        if (result.isSuccess()) {
            List<ApiCardDTO> list = result.getData().getList();
            list.forEach(e -> {
                if (StringUtils.isEmpty(e.getDesc())) {
                    return;
                }
                e.setDesc(e.getDesc().replaceAll("<[^>]+>", ""));
            });
            return result.getData();
        }
        throw new ApiMarketException("查询API市场列表异常:" + result.getMsg());
    }

    @Override
    public List<TagDTO> getTags() {
        CommonResult<List<TagDTO>> result = apiMarketProvider.getTags(createToken());
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new ApiMarketException("获取标签异常:" + result.getMsg());
    }

    @Override
    public ApiDetailInfo getDetail(Long id) {
        CommonResult<ApiDetailDTO> result = apiMarketProvider.getDetail(createToken(), id, Boolean.FALSE);
        if (result.isError()) {
            throw new ApiMarketException("查询API市场详情异常:" + result.getMsg());
        }
        ApiDetailDTO data = result.getData();
        ApiDetailInfo apiDetailInfo = new ApiDetailInfo();
        BeanUtil.copyProperties(data, apiDetailInfo);
        // 接口获取网关
        CommonResult<List<GatewayDTO>> gateway = apiMarketProvider.getGateway(createToken());
        if (gateway.isSuccess()) {
            List<String> urls = gateway.getData().stream().map(GatewayDTO::getShowUrl).collect(Collectors.toList());
            apiDetailInfo.setGateway(urls);
        }
        return apiDetailInfo.init();
    }

    @Override
    public List<OrgDTO> getSubOrg(Long pubOrgId) {
        CommonResult<List<OrgDTO>> result = apiMarketProvider.getSubOrg(createToken(), pubOrgId);
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new ApiMarketException("查询API市场列表异常:" + result.getMsg());
    }

    @Override
    public Boolean subscribeApi(SubscribeApiDTO subscribeApiDTO) {
        String token = createToken();
        User currentUser = RequestUtils.getCurrentUser();
        String id = currentUser.getId();
        Long productId = subscribeApiDTO.getProductId();
        String clientId = subscribeApiDTO.getClientId();
        // 订阅组id
        Long subOrgId = subscribeApiDTO.getSubOrgId();
        String subOrgName = subscribeApiDTO.getSubOrgName();
        String newAppName = subscribeApiDTO.getNewAppName();
        // 新建就一定没有重复订阅，否则要校验
        if (StringUtils.isNotEmpty(clientId)) {
            validApplyStatus(token, clientId, productId, subOrgId);
        }
        // 发起审批流程
        // 获取API详情失败
        CommonResult<ApiDetailDTO> resultDetail = apiMarketProvider.getDetail(token, productId, Boolean.FALSE);
        if (resultDetail.isError()) {
            throw new ApiMarketException("Api详情获取失败:" + resultDetail.getMsg());
        }
        // 发起审批
        ApiDetailDTO data = resultDetail.getData();
        getGateway(data);
        data.setGatewayUrl(data.getGatewayUrl());
        data.setSandboxClientId(clientId);
        data.setSandboxClientSecret(null);
        data.setApiSpec(null);
        data.setSubOrgName(subOrgName);
        ApiApproveDTO apiApproveDTO = ApiApproveDTO.subInit(data.getName(), JSONObject.toJSONString(data),
                String.valueOf(productId));
        CommonResult<String> approveResult = approveProvider.createInstance(RequestUtils.getToken(), apiApproveDTO);
        String instanceId = approveResult.getData();
        String msg = approveResult.getMessage();
        if (approveResult.isError()) {
            throw new ApiMarketException(msg);
        }
        ApiSubscribeRecord apiSubscribeRecord = new ApiSubscribeRecord();
        apiSubscribeRecord.setInstanceId(instanceId);
        apiSubscribeRecord.setProductId(productId);
        apiSubscribeRecord.setClientId(clientId);
        apiSubscribeRecord.setNewAppName(newAppName);
        // 订阅组id
        apiSubscribeRecord.setSubOrgId(subOrgId);
        apiSubscribeRecord.setUserId(id);
        apiSubscribeRecord.setStatus(SubscribeStatusEnum.APPLYING.getCode());
        apiSubscribeRecordMapper.insert(apiSubscribeRecord);
        return Boolean.TRUE;
    }

    private void getGateway(ApiDetailDTO data) {
        try {
            String apiSpec = data.getApiSpec();
            // 网关接口,如果接口有问题 就走默认网关
            CommonResult<List<GatewayDTO>> result = apiMarketProvider.getGateway(createToken());
            if (result.isSuccess()) {
                List<String> urls = result.getData().stream().map(GatewayDTO::getShowUrl).collect(Collectors.toList());
                data.setGatewayUrl(urls);
            } else {
                JsonNode jsonNode = new ObjectMapper().readTree(apiSpec);
                if (jsonNode.has("host")) {
                    data.setGatewayUrl(List.of(jsonNode.get("host").asText()));
                }
            }
        } catch (JsonProcessingException e) {
            throw new ApiMarketException("Api获取网关失败:" + e.getMessage());
        }
    }

    @Override
    public List<String> getApproveUsers(Long id) {
        String token = createToken();
        CommonResult<ApiDetailDTO> detail = apiMarketProvider.getDetail(token, id, Boolean.FALSE);
        if (detail.isError()) {
            throw new ApiMarketException("获取api失败,id:" + id + "。因为:" + detail.getMsg());
        }
        ApiDetailDTO data = detail.getData();
        String pubOrgCode = data.getPubOrgCode();
        Long pubOrgId = data.getPubOrgId();
        return getUsers(token, pubOrgCode, pubOrgId);
    }

    private List<String> getUsers(String token, String pubOrgCode, Long pubOrgId) {
        CommonResult<PubOrgDTO> pubOrgResult = apiMarketProvider.getPubOrg(token, pubOrgCode, pubOrgId);
        if (pubOrgResult.isError()) {
            throw new ApiMarketException("获得iPaaS指定发布组织失败,pubOrgCode:" + pubOrgCode + "。因为:" + pubOrgResult.getMsg());
        }
        // 发布组编码
        if (pubOrgResult.getData() == null || StringUtils.isEmpty(pubOrgResult.getData().getMasterName())) {
            return List.of("144693", "121100");
        }
        String masterName = pubOrgResult.getData().getMasterName();
        return Arrays.stream(masterName.split(",")).collect(Collectors.toList());
    }

    @Override
    public List<String> getApproveUsersByPubOrgId(Long pubOrgId) {
        String token = createToken();
        return getUsers(token, null, pubOrgId);
    }

    @Override
    public ApiApproveDTO rollback(ApiRollbackDTO apiRollbackDTO) {
        String instanceId = apiRollbackDTO.getInstanceId();
        String status = apiRollbackDTO.getStatus();
        if (ApiRollbackStatus.APPROVE.getCode().equals(status)) {
            String token = createToken();
            ApiSubscribeRecord subscribeRecord = apiSubscribeRecordMapper.selectOne(
                    new QueryWrapper<ApiSubscribeRecord>()
                            .eq("instance_id", instanceId));
            Long id = subscribeRecord.getProductId();
            Long subOrgId = subscribeRecord.getSubOrgId();
            String newAppName = subscribeRecord.getNewAppName();
            CommonResult<ApiDetailDTO> detail = apiMarketProvider.getDetail(token, id, Boolean.FALSE);
            if (detail.isError()) {
                throw new ApiMarketException("获取api失败,id:" + id + "。因为:" + detail.getMsg());
            }
            SubscribeApiDTO subscribeApiDTO = new SubscribeApiDTO();
            subscribeApiDTO.setProductId(subscribeRecord.getProductId());
            subscribeApiDTO.setSubOrgId(subOrgId);
            subscribeApiDTO.setClientId(subscribeRecord.getClientId());
            subscribeApiDTO.setNewAppName(newAppName);
            CommonResult<ApiSubResultDTO> result = apiMarketProvider.subscribeApi(token, subscribeApiDTO);
            if (result.isError()) {
                throw new ApiMarketException("api订阅失败,id:" + subscribeRecord.getProductId() + "。因为:" + result.getMsg());
            }
            ApiSubResultDTO resultData = result.getData();
            ApiApproveDTO apiApproveDTO = new ApiApproveDTO();
            ApiDetailDTO data = detail.getData();
            CommonResult<List<OrgDTO>> subOrgResult = apiMarketProvider.getSubOrg(createToken(), data.getPubOrgId());
            if (subOrgResult.isError()) {
                throw new ApiMarketException("获取发布组信息失败:" + subOrgResult.getMsg());
            }
            List<OrgDTO> subOrgResultData = subOrgResult.getData();
            OrgDTO orgDTO = subOrgResultData.stream().filter(e -> subOrgId.equals(e.getId())).findFirst()
                    .orElse(new OrgDTO());
            data.setSubOrgName(orgDTO.getName());
            data.setSandboxClientId(resultData.getClientId());
            data.setSandboxClientSecret(resultData.getClientSecret());
            getGateway(data);
            data.setApiSpec(null);
            apiApproveDTO.setBusinessData(JSONObject.toJSONString(data));
            apiSubscribeRecordMapper.update(
                    new ApiSubscribeRecord()
                            .setStatus(SubscribeStatusEnum.APPROVED.getCode())
                            .setClientId(resultData.getClientId()),
                    new QueryWrapper<ApiSubscribeRecord>()
                            .eq("instance_id", instanceId));
            return apiApproveDTO;
        }
        apiSubscribeRecordMapper.update(
                new ApiSubscribeRecord()
                        .setStatus(SubscribeStatusEnum.REJECTED.getCode()),
                new QueryWrapper<ApiSubscribeRecord>()
                        .eq("instance_id", instanceId));
        return null;
    }

    @Override
    public SubLogDTO getSubLogs(String name, String requestBody, String responseBody, String responseStatus,
                                String startTime, String endTime, Integer pageSize, Integer pageNo) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            startTime = null;
            endTime = null;
        }
        CommonResult<SubLogDTO> result = apiMarketProvider.getSubLogs(createToken(), name, requestBody,
                responseBody, responseStatus, startTime, endTime, pageSize, pageNo);
        if (result.isError()) {
            throw new ApiMarketException("获取API订阅日志列表查询异常:" + result.getMsg());
        }
        SubLogDTO data = result.getData();
        data.getList().forEach(e -> e.setDatetime(convertDateTime(e.getDatetime())));
        return data;
    }

    public static String convertDateTime(String input) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX")
                .withZone(ZoneId.of("UTC"));

        DateTimeFormatter outFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            ZonedDateTime zdt = ZonedDateTime.parse(input, inputFormatter);
            return zdt.format(outFormatter);
        } catch (DateTimeParseException e) {
            // 处理格式错误的情况
            throw new IllegalArgumentException("Invalid date format: " + input, e);
        }
    }

    @Override
    public LogDetailDTO getSubLogDetail(String eventId) {
        CommonResult<LogDetailDTO> subLogDetail = apiMarketProvider.getSubLogDetail(createToken(), eventId);
        if (subLogDetail.isError()) {
            throw new ApiMarketException("获取API:" + eventId + ",详情失败原因：" + subLogDetail.getMsg());
        }
        return subLogDetail.getData();
    }

    @Override
    public OrderPage getMyOrderPage(String productName, String apiPath, String pubOrgName, String startTime,
                                    String endTime, Integer pageSize, Integer pageNo) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            startTime = null;
            endTime = null;
        }
        CommonResult<OrderPage> myOrderPage = apiMarketProvider.getMyOrderPage(createToken(), productName, apiPath,
                pubOrgName, startTime, endTime, pageSize, pageNo);
        if (myOrderPage.isError()) {
            throw new ApiMarketException("获取我的订阅列表失败：" + myOrderPage.getMsg());
        }
        myOrderPage.getData().getList()
                .forEach(e -> e.setCreateTime(DateUtil.convertWithPattern(Long.parseLong(e.getCreateTime()))));
        return myOrderPage.getData();

    }

    @Override
    public Map<String, Object> sendApi(SendApiDTO sendApiDTO) {
        HttpHeaders headers = createHeaders(sendApiDTO);
        HttpEntity<?> requestEntity = createRequestEntity(sendApiDTO, headers);
        HttpMethod httpMethod = getHttpMethod(sendApiDTO);
        try {
            long startTime = System.currentTimeMillis();
            String url = buildUrl(sendApiDTO.getUrl(), sendApiDTO.getPathParams(), sendApiDTO.getQueryParams());
            log.info("Sending request to URL: {}", url);
            ResponseEntity<String> response = null;
            try {
                ResponseEntity<byte[]> responseBytes = restTemplate.exchange(url, httpMethod, requestEntity, byte[].class);
                byte[] responseBodyBytes = responseBytes.getBody();
                String responseBody = (responseBodyBytes != null) ? new String(responseBodyBytes, StandardCharsets.ISO_8859_1) : "";
                response = new ResponseEntity<>(responseBody, responseBytes.getStatusCode());
            } catch (HttpStatusCodeException e) {
                // Handle all HTTP status exceptions (4xx and 5xx)
                byte[] errorBodyBytes = e.getResponseBodyAsByteArray();
                String errorBody = new String(errorBodyBytes, StandardCharsets.ISO_8859_1);
                response = new ResponseEntity<>(errorBody, e.getStatusCode());
            } finally {
                long timeCost = System.currentTimeMillis() - startTime;
                Map<String, Object> result = new HashMap<>();
                result.put("time", timeCost);
                if (response != null) {
                    result.put("status", response.getStatusCodeValue());
                    result.put("headers", response.getHeaders());
                    result.put("data", response.getBody());
                } else {
                    result.put("status", 500);
                    result.put("data", "Request failed due to unexpected error");
                    result.put("headers", new HttpHeaders());
                }
                result.put("requestUrl", url);
                result.put("requestHeaders", requestEntity.getHeaders());
                result.put("requestBody", requestEntity.getBody());
                return result;
            }
        } catch (Exception e) {
            log.error("请求转发失败: ", e);
            throw new ApiMarketException("请求转发失败: " + e.getMessage());
        }
    }


    @Override
    public Boolean apiPub(JSONObject jsonObject) {
        User currentUser = RequestUtils.getCurrentUser();
        Long pubOrgId = Long.valueOf(jsonObject.getString("pubOrgId"));
        String apiName = jsonObject.getString("code");
        String json = jsonObject.toString();
        ApiApproveDTO apiApproveDTO = ApiApproveDTO.pubInit(apiName, json, pubOrgId);
        CommonResult<String> approveResult = approveProvider.createInstance(RequestUtils.getToken(), apiApproveDTO);
        if (approveResult.isError()) {
            throw new ApiMarketException("创建发布流程失败，因为：" + approveResult.getMessage());
        }
        String instanceId = approveResult.getData();
        ApiPublishRecord apiPublishRecord = new ApiPublishRecord();
        apiPublishRecord.setUserId(currentUser.getId())
                .setPubOrgId(pubOrgId)
                .setInstanceId(instanceId)
                .setRequestJson(json)
                .setStatus(0);
        // 保存到数据库
        apiPublishRecordMapper.insert(apiPublishRecord);
        return true;
    }

    @Override
    public ApiApproveDTO pubRollback(ApiRollbackDTO apiRollbackDTO) {
        String instanceId = apiRollbackDTO.getInstanceId();
        String status = apiRollbackDTO.getStatus();
        //查询发布记录
        ApiPublishRecord apiPublishRecord = apiPublishRecordMapper.selectOne(
                new QueryWrapper<ApiPublishRecord>()
                        .eq("instance_id", instanceId));
        if (apiPublishRecord == null) {
            throw new ApiMarketException("未找到对应的发布记录");
        }
        String requestJson = apiPublishRecord.getRequestJson();
        JSONObject requestJsonObject = JSONObject.parseObject(requestJson);
        // 处理审批通过
        if (ApiRollbackStatus.APPROVE.getCode().equals(status)) {
            try {
                String token = RequestUtils.getToken();
                CommonResult<Integer> result = apiMarketProvider.publishApi(token, requestJsonObject);
                if (result.isError()) {
                    throw new ApiMarketException("api发布失败:" + result.getMsg());
                }
                ApiApproveDTO apiApproveDTO = new ApiApproveDTO();
                apiApproveDTO.setBusinessData(requestJson);
                apiPublishRecordMapper.update(
                        new ApiPublishRecord()
                                .setStatus(SubscribeStatusEnum.APPROVED.getCode()),
                        new QueryWrapper<ApiPublishRecord>()
                                .eq("instance_id", instanceId));
                return apiApproveDTO;
            } catch (Exception e) {
                log.error("发布回调处理异常", e);
                throw new ApiMarketException("发布回调处理异常");
            }
        }
        // 处理拒绝
        apiPublishRecordMapper.update(
                new ApiPublishRecord()
                        .setStatus(SubscribeStatusEnum.REJECTED.getCode()),
                new QueryWrapper<ApiPublishRecord>()
                        .eq("instance_id", instanceId));
        return null;
    }

    private static HttpMethod getHttpMethod(SendApiDTO sendApiDTO) {
        // 验证HTTP方法
        if (sendApiDTO.getMethod() == null || sendApiDTO.getMethod().trim().isEmpty()) {
            throw new ApiMarketException("HTTP方法不能为空");
        }
        HttpMethod httpMethod = HttpMethod.resolve(sendApiDTO.getMethod().toUpperCase());
        if (httpMethod == null) {
            throw new ApiMarketException("不支持的HTTP方法: " + sendApiDTO.getMethod());
        }
        return httpMethod;
    }

    private HttpHeaders createHeaders(SendApiDTO sendApiDTO) {
        HttpHeaders headers = new HttpHeaders();
        if (sendApiDTO.getHeader() != null) {
            headers.setAll(sendApiDTO.getHeader());
        }
        return headers;
    }

    private HttpEntity<?> createRequestEntity(SendApiDTO sendApiDTO, HttpHeaders headers) {
        if (headers.getContentType() != null
                && MediaType.MULTIPART_FORM_DATA.isCompatibleWith(headers.getContentType())) {
            return createMultipartRequestEntity(sendApiDTO, headers);
        } else if (isFormUrlEncoded(headers)) {
            return createFormUrlEncodedRequestEntity(sendApiDTO, headers);
        } else {
            // 显式设置JSON内容类型
            headers.setContentType(MediaType.APPLICATION_JSON);
            return new HttpEntity<>(JSONObject.toJSONString(sendApiDTO.getBody()), headers);
        }
    }

    private boolean isFormUrlEncoded(HttpHeaders headers) {
        return MediaType.APPLICATION_FORM_URLENCODED_VALUE.equals(headers.getContentType());
    }

    private HttpEntity<MultiValueMap<String, Object>> createMultipartRequestEntity(SendApiDTO sendApiDTO,
                                                                                   HttpHeaders headers) {
        MultiValueMap<String, Object> multipartBody = new LinkedMultiValueMap<>();
        addFilesToMultipartBody(sendApiDTO, multipartBody);
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        return new HttpEntity<>(multipartBody, headers);
    }

    private void addFilesToMultipartBody(SendApiDTO sendApiDTO, MultiValueMap<String, Object> multipartBody) {
        for (Map.Entry<String, MultipartFile> entry : sendApiDTO.getFiles().entrySet()) {
            // 处理模拟文件数据（内存数据转Resource）
            byte[] fileData = null;
            try {
                fileData = entry.getValue().getBytes();
            } catch (IOException e) {
                throw new ApiMarketException(e);
            }
            ByteArrayResource resource = new ByteArrayResource(fileData) {
                @Override
                public String getFilename() {
                    return entry.getValue().getOriginalFilename();
                }
            };
            multipartBody.add(entry.getKey(), resource);
        }
    }


    private HttpEntity<MultiValueMap<String, String>> createFormUrlEncodedRequestEntity(SendApiDTO sendApiDTO,
                                                                                        HttpHeaders headers) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        if (sendApiDTO.getBody() != null) {
            String[] params = ((String) sendApiDTO.getBody()).split("&");
            for (String param : params) {
                if (param.contains("=")) {
                    String[] keyValue = param.split("=", 2);
                    formData.add(keyValue[0], keyValue.length > 1 ? keyValue[1] : "");
                }
            }
        }
        return new HttpEntity<>(formData, headers);
    }

    private String buildUrl(String originalUrl, Map<String, String> pathParams, Map<String, String> queryParams) {
        // 处理null参数情况
        Map<String, String> safePathParams = (pathParams != null) ? pathParams : Collections.emptyMap();
        Map<String, String> safeQueryParams = (queryParams != null) ? queryParams : Collections.emptyMap();

        // 替换路径参数
        UriTemplate uriTemplate = new UriTemplate(originalUrl);
        String expandedUrl = uriTemplate.expand(safePathParams).toString();

        // 添加查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(expandedUrl);
        safeQueryParams.forEach(builder::queryParam);
        return builder.toUriString();
    }

    private void validApplyStatus(String token, String clientId, Long productId, Long subOrgId) {
        QueryWrapper<ApiSubscribeRecord> statusQuery = new QueryWrapper<>();
        // 查询状态为申请中（0）或通过（1）的记录
        statusQuery.eq("product_id", productId)
                .eq("sub_org_id", subOrgId)
                .eq("client_id", clientId)
                .in("status",
                        SubscribeStatusEnum.APPLYING.getCode(),
                        SubscribeStatusEnum.APPROVED.getCode());
        Long existingCount = apiSubscribeRecordMapper.selectCount(statusQuery);
        if (existingCount > 0) {
            // 存在符合条件的记录（申请中或已通过）
            throw new ApiException("当前产品已存在有效订阅申请，无需重复提交");
        }
        // 判断在ipaas中再判断是否重复订阅，避免后续走审批
        CommonResult check = apiMarketProvider.check(token, productId, subOrgId, clientId);
        if (check.isError()) {
            // 存在符合条件的记录（已通过）
            throw new ApiException("当前产品已存在有效订阅申请，无需重复提交");
        }
    }
}
