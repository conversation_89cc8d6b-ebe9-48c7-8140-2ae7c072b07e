 package com.trinasolar.integration.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.dto.appLogs.SreLogPageDTO;
import com.trinasolar.integration.dto.appLogs.SreLogQueryDTO;
import com.trinasolar.integration.dto.appLogs.SreLogResultDTO;
import com.trinasolar.integration.outside.SreLogClient;
import com.trinasolar.integration.service.AppLogsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class AppLogsServiceImpl implements AppLogsService {

    @Autowired
    private SreLogClient sreLogClient;

    @Autowired
    private ProjectConfigMapper projectConfigMapper;

    @Autowired
    private ApplicationProgramMapper applicationProgramMapper;
    @Override
    public SreLogPageDTO searchAppLogs(SreLogQueryDTO sreLogQueryDTO) {
        SreLogPageDTO appLogs = new SreLogPageDTO();
        sreLogQueryDTO.setPage(sreLogQueryDTO.getCurrent());
        appLogs.setCurrent(sreLogQueryDTO.getCurrent());
        appLogs.setPage(sreLogQueryDTO.getCurrent());
        appLogs.setSize(sreLogQueryDTO.getSize());
        appLogs.setTotal(0);
        appLogs.setRecords(new ArrayList<>());
        if (StringUtils.isEmpty(sreLogQueryDTO.getAppName())){
            // 1.先根据projectId查询出项目配置信息
            if (StringUtils.isNotEmpty(sreLogQueryDTO.getSysName()) && Objects.nonNull(sreLogQueryDTO.getSysId())){
                ProjectConfigDO projectConfigDO = projectConfigMapper.selectProjectConfigByProjectId(sreLogQueryDTO.getSysId());
                if (Objects.nonNull(projectConfigDO) && StringUtils.isNotBlank(projectConfigDO.getConfigContent())){
                    Map namespaceMap = JSONUtil.toBean(projectConfigDO.getConfigContent(), Map.class);
                    String namespace = (String) namespaceMap.get("PROD");
                    // 如果结尾包含--prod，则截取掉倒数6位
                    if (StringUtils.isNotBlank(namespace) && namespace.endsWith("--prod")) {
                        namespace = namespace.substring(0, namespace.length() - 6);
                    }
                    sreLogQueryDTO.setNamespaceSysName(namespace);
                }else {
                    // 1.2 如果项目配置信息不存在，则直接返回
                    log.warn("项目配置信息不存在，查询条件：{}", JSON.toJSON(sreLogQueryDTO));
                    return appLogs;
                }
            }
            // 2.查询应用程序名称
            if (StringUtils.isEmpty(sreLogQueryDTO.getSysName()) && Objects.nonNull(sreLogQueryDTO.getSysId())){
                sreLogQueryDTO.setAppId(sreLogQueryDTO.getSysId());
                ApplicationProgram applicationProgram = applicationProgramMapper.selectById(sreLogQueryDTO.getAppId());
                if (Objects.nonNull(applicationProgram)){
                    sreLogQueryDTO.setAppName(applicationProgram.getProgramNameEn());
                }else {
                    // 2.2 如果应用程序名称不存在，则直接返回
                    log.warn("应用程序名称不存在，查询条件：{}", JSON.toJSON(sreLogQueryDTO));
                    return appLogs;
                }
            }
        }

        // 3.先调用日志易-搜索任务提交接口
        SreLogResultDTO resultDTO = sreLogClient.searchSubmit(sreLogQueryDTO);
        if (Objects.nonNull(resultDTO)) {
            // 最大重试次数和每次延迟时间（建议配置化）
            int maxRetries = 4;
            long delayMillis = 600;
            int retryCount = 0;
            boolean isReady = false;
            // 循环等待任务就绪或达到最大重试次数
            while (!isReady && retryCount < maxRetries) {
                try {
                    // 4.延迟后检查任务状态（或直接调用结果接口）
                    Thread.sleep(delayMillis);
                    log.info("第 {} 次尝试获取结果（延迟 {}ms）", retryCount + 1, delayMillis);
                    appLogs = sreLogClient.searchFetch(sreLogQueryDTO, resultDTO);
                    if (appLogs.isHasData()) {
                        isReady = true;
                    }else {
                        retryCount++;
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("延迟等待被中断", e);
                    return appLogs;
                } catch (Exception e) {
                    retryCount++;
                    log.warn("第 {} 次获取结果失败，将重试", retryCount, e);
                    if (retryCount >= maxRetries) {
                        return appLogs;
                    }
                }
            }
        }
        return appLogs;
    }

     @Override
     public void downloadAppLogs(SreLogQueryDTO sreLogQueryDTO, HttpServletResponse response) {
         // 先调用日志易-搜索任务提交接口
         SreLogResultDTO resultDTO = sreLogClient.searchSubmit(sreLogQueryDTO);
     }
 }

