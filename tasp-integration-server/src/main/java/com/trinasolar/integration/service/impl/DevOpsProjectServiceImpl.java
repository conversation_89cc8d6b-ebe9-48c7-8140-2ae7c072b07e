package com.trinasolar.integration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.api.vo.UpdateMemberDTO;
import com.trinasolar.integration.constants.GitLabAccessLevel;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectAdminDTO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectResp;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectSaveReqVO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.integration.dao.AppSystemInitializeMapper;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.service.BaseDevOpsService;
import com.trinasolar.integration.service.DevOpsProjectService;
import com.trinasolar.integration.service.GitService;
import com.trinasolar.tasc.framework.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.trinasolar.integration.util.ProjectConfigConstants.DEVOPS_BASIC_INFO_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DevOpsProjectServiceImpl extends BaseDevOpsService implements DevOpsProjectService {
    @Resource
    private AppSystemInitializeMapper appSystemInitializeMapper;
    @Resource
    private GitService gitService;
    @Autowired
    private ProjectConfigMapper projectConfigMapper;

    @Override
    public DevOpsProjectResp<Boolean> updateProject(Long id, List<String> delCodes, List<String> addCodes) {
        String projectCode = getProjectCode(id);
        if (StrUtil.isEmpty(projectCode)) {
            throw new ServiceException(10010, "Devops项目不存在");
        }
        String englishNameCustom = getProjectEnglishNameCustom(id);
        String projectName = getProjectName(id);
        DevOpsProjectResp<List<DevOpsProjectAdminDTO>> adminList = getAdminList(projectCode);
        List<String> userCodes = adminList.getData().stream().map(DevOpsProjectAdminDTO::getUserId).collect(Collectors.toList());
        userCodes.addAll(addCodes);
        userCodes.removeAll(delCodes);
        String addUserCodesStr = userCodes.stream().distinct().collect(Collectors.joining(","));
        DevOpsProjectSaveReqVO reqVO = new DevOpsProjectSaveReqVO();
        reqVO.setProjectName(projectName).setEnglishNameCustom(englishNameCustom)
                .setAdministrator(addUserCodesStr);
        String updateProjectUrl = DOMAIN_HOST + "/ms/projectmanager/api/user/projectProps/" + projectCode + "?issueId=";
        try (HttpResponse response = HttpRequest.
                put(updateProjectUrl).
                cookie(getSessionCookie()).
                contentType("application/json").
                header("Referer", DOMAIN_HOST + "/console/platform/entry").
                header("Origin", DOMAIN_HOST).body(JSONUtil.toJsonStr(reqVO))
                .execute()) {
            if (response.getStatus() == 200) {
                ObjectMapper mapper = new ObjectMapper();
                return mapper.readValue(response.body(), new TypeReference<DevOpsProjectResp<Boolean>>() {
                });
            }
        } catch (Exception e) {
            log.error("修改devops用户异常删除{}，添加{}", delCodes, addCodes, e);
        }
        return null;
    }

    @Override
    public DevOpsProjectResp<List<DevOpsProjectAdminDTO>> getAdminList(String projectCode) {
        String adminListUrl = DOMAIN_HOST + "/ms/auth/api/user/projectId/" + projectCode + "/admin/list/all";
        try (HttpResponse response = HttpRequest.
                get(adminListUrl).
                cookie(getSessionCookie()).
                contentType("application/json").
                header("Referer", DOMAIN_HOST + "/console/platform/entry").
                header("Origin", DOMAIN_HOST)
                .execute()) {
            String result = response.body();
            log.info("DevOps项目创建结果：" + result);
            if (response.getStatus() == 200) {
                ObjectMapper mapper = new ObjectMapper();
                return mapper.readValue(response.body(), new TypeReference<DevOpsProjectResp<List<DevOpsProjectAdminDTO>>>() {
                });
            }
        } catch (Exception e) {
            log.error("获取devops用户异常", e);
        }
        return null;
    }

    @Override
    public DevOpsProjectRespVO createProject(DevOpsProjectSaveReqVO dto) {
        DevOpsProjectRespVO createResult = getExistProject(dto.getProjectName());
        if (createResult == null) {
            boolean flag1 = validateProjectName(dto.getProjectName(), getSessionCookie());
            boolean flag2 = validateEnglishName(dto.getEnglishNameCustom(), getSessionCookie());
            if (flag1 && flag2) {
                String formJson = JSONUtil.toJsonStr(dto);
                log.info("DevOps项目创建入参：" + formJson);
                String createProjectUrl = DOMAIN_HOST + "/ms/projectmanager/api/user/projectProps?issueId=";
                try (HttpResponse response = HttpRequest.
                        post(createProjectUrl).
                        cookie(getSessionCookie()).
                        contentType("application/json").
                        header("Referer", DOMAIN_HOST + "/console/platform/entry").
                        header("Origin", DOMAIN_HOST).
                        body(formJson).execute()) {
                    String result = response.body();
                    log.info("DevOps项目创建结果：" + result);
                    if (response.getStatus() == 200) {
                        if (!StrUtil.isEmpty(result)) {
                            JSONObject json = JSONUtil.parseObj(result);
                            if (json.getInt("code") == 0) {
                                //再获取一次
                                createResult = getExistProject(dto.getProjectName());
                                return createResult;
                            }
                        }
                    }
                }
            } else if (!flag1) {
                log.error("项目中文名称校验失败，无法创建项目");
                throw new ServiceException(10010, "项目中文名称校验失败，无法创建项目");
            } else if (!flag2) {
                log.error("项目英文名称校验失败，无法创建项目");
                throw new ServiceException(10010, "项目英文名称校验失败，无法创建项目");
            }
        }
        return createResult;
    }


    @Async
    @Override
    public void addCicdPlugins(String projectId) {
        String addCicdPluginsUrl = DOMAIN_HOST + "/ms/store/api/user/market/atom/install/AtomsToProjects";
        String formData = "{\n" +
                "  \"projectCode\": [\n" +
                "    \"" + projectId + "\"\n" +
                "  ],\n" +
                "  \"atomCode\": [\n" +
                "    \"CODE_GITLAB\",\n" +
                "    \"executeJobScriptNew\",\n" +
                "    \"jobEeExecuteTaskExt\",\n" +
                "    \"timerTrigger\",\n" +
                "    \"subPipelineCall\",\n" +
                "    \"GiteePullCode\",\n" +
                "    \"FlowInIASTTrinasolarAfter\",\n" +
                "    \"ProductUpgradeBkRepo\",\n" +
                "    \"PipelineDispatchConf\",\n" +
                "    \"HelmForRepo\",\n" +
                "    \"FlowInlSCA\",\n" +
                "    \"codeCodingWebHookTrigger\",\n" +
                "    \"codeGFGitWebHookTrigger\",\n" +
                "    \"GerritPullCode\",\n" +
                "    \"InsertMetadata\",\n" +
                "    \"Bitbucket\",\n" +
                "    \"singleArchive\",\n" +
                "    \"SonarQube\",\n" +
                "    \"tfsGit\",\n" +
                "    \"WriteMetadata\",\n" +
                "    \"ThirdDockerBuildAndPushImage\",\n" +
                "    \"codeGerritWebHookTrigger\",\n" +
                "    \"codeAntCodeWebHookTrigger\",\n" +
                "    \"GFGit\",\n" +
                "    \"customizeArchiveGet\",\n" +
                "    \"CodingPullCode\",\n" +
                "    \"KubernetesDeployDelete\",\n" +
                "    \"ConfDeployAppconfig\",\n" +
                "    \"PipelineArchiveArtifact\",\n" +
                "    \"ProductUpgradeBkRepoQuality\",\n" +
                "    \"MetaDataAccessControl\",\n" +
                "    \"DockerBuildAndPushImage\",\n" +
                "    \"InsertCommitInfo\",\n" +
                "    \"CODE_SVN\",\n" +
                "    \"TagTaskScript\",\n" +
                "    \"GITHUB\",\n" +
                "    \"PushJobFileAppconfig\",\n" +
                "    \"FlowInSCATrinasolar\",\n" +
                "    \"GetCustomizeArchiveBkRepoCroEnv\",\n" +
                "    \"manualReviewUserTask\",\n" +
                "    \"codeSVNWebHookTrigger\",\n" +
                "    \"codeGiteeWebHookTrigger\",\n" +
                "    \"codeGitlabWebHookTrigger\",\n" +
                "    \"EeJobDevOpsFastExecuteScript\",\n" +
                "    \"FastExecuteSql\",\n" +
                "    \"MavenDeploy\",\n" +
                "    \"TestNG\",\n" +
                "    \"WriteReportUrl\",\n" +
                "    \"JarGitDiff\",\n" +
                "    \"BuildPiplineArchiveGetTask\",\n" +
                "    \"CustomizeArchiveArtifact\",\n" +
                "    \"tfs\",\n" +
                "    \"WorkOrderApprove\",\n" +
                "    \"jobEeDistribution\",\n" +
                "    \"ApolloDashboard\",\n" +
                "    \"GetVlanAndIpAtomV2\",\n" +
                "    \"GetVlanAndIp\",\n" +
                "    \"SelectArchivedCoverage\",\n" +
                "    \"buildArchiveGet\",\n" +
                "    \"windowsScript\",\n" +
                "    \"SelectArchivedInformation\",\n" +
                "    \"metadata\",\n" +
                "    \"KubernetesDeploy\",\n" +
                "    \"CrossProjectDispatchPipeline\",\n" +
                "    \"MeterSphere\",\n" +
                "    \"ExecuteJobScriptRuralBank\",\n" +
                "    \"AndroidCertInstall\",\n" +
                "    \"git\",\n" +
                "    \"InterpretedLanguageGitDiff\",\n" +
                "    \"FlowInIASTTrinasolarBefore\",\n" +
                "    \"ArtifactoryCopyBkRepo\",\n" +
                "    \"GetAppReversion\",\n" +
                "    \"buildPushDockerImage\",\n" +
                "    \"sendWechatNotify\",\n" +
                "    \"CoverageIncrementalComparison\",\n" +
                "    \"PullHelmPackage\",\n" +
                "    \"manualTrigger\",\n" +
                "    \"sendEmailNotify\",\n" +
                "    \"linuxScript\",\n" +
                "    \"ExecuteJobScriptAppconfig\",\n" +
                "    \"SonarQubeQuality\",\n" +
                "    \"ArchiveArtifacty\",\n" +
                "    \"GitLabTagging\",\n" +
                "    \"BuildCustomerArchiveGetTask\",\n" +
                "    \"CustomizeArchiveArtifactBkRepo\",\n" +
                "    \"remoteTrigger\",\n" +
                "    \"Jacoco\",\n" +
                "    \"GenerateWorkReport\",\n" +
                "    \"bladeTest\",\n" +
                "    \"PipelineArtifactBkrepo\",\n" +
                "    \"IOSCertInstall\",\n" +
                "    \"DependencyCheck\",\n" +
                "    \"reportArchive\",\n" +
                "    \"codeGithubWebHookTrigger\",\n" +
                "    \"gcloudEe\",\n" +
                "    \"ArtifactoryCopy\",\n" +
                "    \"VMDeployReport\",\n" +
                "    \"WriteWorkItem\",\n" +
                "    \"HelmForLocal\"\n" +
                "  ]\n" +
                "}";
        try (HttpResponse response = HttpRequest
                .post(addCicdPluginsUrl)
                .cookie(getSessionCookie())
                .contentType("application/json")
                .body(formData).execute()) {
            if (response.getStatus() == 200) {
                String result = response.body();
                if (!StrUtil.isEmpty(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json.getInt("status") == 0) {
                        log.info("插件创建成功");
                    }
                }
            }
        }
    }

    @Override
    public Map<String, String> addEnvGroup(String projectId) {
        Map<String, String> results = new HashMap<>();
        String existEnvGroupUrl = DOMAIN_HOST + "/ms/process/api/user/pipelineViews/projects/" + projectId + "/list"; //查询项目已经存在的环境分组信息
        HttpResponse searchResp = HttpRequest.get(existEnvGroupUrl).cookie(getSessionCookie()).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    //已存在，返回已存在的HashID
                    JSONArray result = json.getJSONArray("data");
                    for (int i = 0; i < result.size(); i++) {
                        JSONObject tmp = result.getJSONObject(i);
                        results.put(tmp.getStr("name"), tmp.getStr("id"));
                    }
                }
            }
        }
        String addEnvGroupUrl = DOMAIN_HOST + "/ms/process/api/user/pipelineViews/projects/" + projectId;
        String tempJson = "{\n" +
                "    \"name\": \"[ENVTXT]\",\n" +
                "    \"projected\": true,\n" +
                "    \"viewType\": 2,\n" +
                "    \"logic\": \"AND\",\n" +
                "    \"filters\": [],\n" +
                "    \"pipelineIds\": [],\n" +
                "    \"pipelineCount\": 0\n" +
                "}";
        if (StrUtil.isEmpty(results.get("DEV"))) {
            //开发分组初始化
            String devGroupId = "";
            HttpResponse devResponse = HttpRequest
                    .post(addEnvGroupUrl)
                    .cookie(getSessionCookie())
                    .contentType("application/json")
                    .body(tempJson.replace("[ENVTXT]", "DEV")).execute();
            if (devResponse.getStatus() == 200) {
                String result = devResponse.body();
                if (!StrUtil.isEmpty(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json.getInt("status") == 0) {
                        devGroupId = json.getJSONObject("data").getStr("id");
                    }
                }
            }
            results.put("DEV", devGroupId);
            log.info("DEV流水线分组：" + devGroupId);
        }
        if (StrUtil.isEmpty(results.get("TEST"))) {
            //测试分组初始化
            String testGroupId = "";
            HttpResponse testResponse = HttpRequest
                    .post(addEnvGroupUrl)
                    .cookie(getSessionCookie())
                    .contentType("application/json")
                    .body(tempJson.replace("[ENVTXT]", "TEST")).execute();
            if (testResponse.getStatus() == 200) {
                String result = testResponse.body();
                if (!StrUtil.isEmpty(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json.getInt("status") == 0) {
                        testGroupId = json.getJSONObject("data").getStr("id");
                    }
                }
            }
            results.put("TEST", testGroupId);
            log.info("TEST流水线分组：" + testGroupId);
        }
        if (StrUtil.isEmpty(results.get("UAT"))) {
            //UAT分组初始化
            String uatGroupId = "";
            HttpResponse uatResponse = HttpRequest
                    .post(addEnvGroupUrl)
                    .cookie(getSessionCookie())
                    .contentType("application/json")
                    .body(tempJson.replace("[ENVTXT]", "UAT")).execute();
            if (uatResponse.getStatus() == 200) {
                String result = uatResponse.body();
                if (!StrUtil.isEmpty(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json.getInt("status") == 0) {
                        uatGroupId = json.getJSONObject("data").getStr("id");
                    }
                }
            }
            results.put("UAT", uatGroupId);
            log.info("UAT流水线分组：" + uatGroupId);
        }
        if (StrUtil.isEmpty(results.get("PROD"))) {
            //PROD分组初始化
            String prodGroupId = "";
            HttpResponse prodResponse = HttpRequest
                    .post(addEnvGroupUrl)
                    .cookie(getSessionCookie())
                    .contentType("application/json")
                    .body(tempJson.replace("[ENVTXT]", "PROD")).execute();
            if (prodResponse.getStatus() == 200) {
                String result = prodResponse.body();
                if (!StrUtil.isEmpty(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json.getInt("status") == 0) {
                        prodGroupId = json.getJSONObject("data").getStr("id");
                    }
                }
            }
            results.put("PROD", prodGroupId);
            log.info("PROD流水线分组：" + prodGroupId);
        }
        return results;
    }

    @Override
    public void enableDockerImgRepoPublic(String projectId) {
        //仓库设置为公开
        String formData1 = "{\n" +
                "    \"public\": true,\n" +
                "    \"description\": \"storage for devops ci docker-local\",\n" +
                "    \"configuration\": {\n" +
                "        \"type\": \"local\",\n" +
                "        \"webHook\": {\n" +
                "            \"webHookList\": []\n" +
                "        },\n" +
                "        \"cleanStrategy\": null,\n" +
                "        \"settings\": {\n" +
                "            \"system\": false\n" +
                "        }\n" +
                "    }\n" +
                "}";
        String enableUrl = DOCKER_IMG_DOMAIN_HOST + "/web/repository/api/repo/update/" + projectId + "/docker-local";
        HttpRequest.post(enableUrl).cookie(getSessionCookie())
                .contentType("application/json")
                .body(JSONUtil.toJsonStr(formData1))
                .execute();

        //保留最近3个副本，7天
        String formData2 = "{\n" +
                "    \"configuration\": {\n" +
                "        \"type\": \"local\",\n" +
                "        \"webHook\": {\n" +
                "            \"webHookList\": []\n" +
                "        },\n" +
                "        \"cleanStrategy\": {\n" +
                "            \"autoClean\": true,\n" +
                "            \"reserveVersions\": \"3\",\n" +
                "            \"reserveDays\": \"7\",\n" +
                "            \"rule\": {\n" +
                "                \"relation\": \"AND\",\n" +
                "                \"rules\": []\n" +
                "            }\n" +
                "        },\n" +
                "        \"settings\": {\n" +
                "            \"system\": false\n" +
                "        }\n" +
                "    }\n" +
                "}";
        HttpRequest.post(enableUrl).cookie(getSessionCookie())
                .contentType("application/json")
                .body(JSONUtil.toJsonStr(formData2))
                .execute();

    }

    @Async
    @Override
    public void relatedAllCredentials(String projectId) {
        HttpResponse listResp = HttpRequest.get(DOMAIN_HOST + "/ms/ticket/api/user/global/credentials/data?page=1&pageSize=100").
                cookie(getSessionCookie()).execute();
        List<String> credentialsList = new ArrayList<>();
        if (listResp.getStatus() == 200) {
            String searchResult = listResp.body();
            if (!StrUtil.isEmpty(searchResult)) {
                JSONObject listJson = JSONUtil.parseObj(searchResult);
                if (listJson.getInt("status") == 0) {
                    JSONObject sdata = listJson.getJSONObject("data");
                    JSONArray records = sdata.getJSONArray("records");
                    for (int i = 0; i < records.size(); i++) {
                        JSONObject credential = records.getJSONObject(i);
                        // 必要的凭证进行初始化
                        if ("devk8s".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "testk8s".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "uatk8s".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "bkrepo".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "new_gitlab".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "old_gitlab".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "prod_ne_k8s".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "prod_se_k8s".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "tscz_n_test".equalsIgnoreCase(credential.getStr("credentialId"))
                                || "tscz_n_uat".equalsIgnoreCase(credential.getStr("credentialId"))
                        ) {
                            credentialsList.add(credential.getStr("credentialId"));
                        }
                    }
                }
            }
        }
        credentialsList.forEach(credential -> {
            //每个凭证加上新建的项目
            try (HttpResponse existProjResp = HttpRequest.get(DOMAIN_HOST + "/ms/ticket/api/user/global/credentials/rel/" + credential + "/projects")
                    .cookie(getSessionCookie()).execute()) {
                if (existProjResp.getStatus() == 200) {
                    List<String> existProjIds = new ArrayList<>();
                    String searchResult = existProjResp.body();
                    if (!StrUtil.isEmpty(searchResult)) {
                        JSONObject listJson = JSONUtil.parseObj(searchResult);
                        if (listJson.getInt("status") == 0) {
                            JSONObject sdata = listJson.getJSONObject("data");
                            JSONArray projects = sdata.getJSONArray("projects");
                            for (int i = 0; i < projects.size(); i++) {
                                JSONObject projJson = projects.getJSONObject(i);
                                existProjIds.add(projJson.getStr("projectId"));
                            }
                        }
                    }
                    existProjIds.add(projectId);
                    JSONObject postData = new JSONObject();
                    postData.putOnce("projects", existProjIds.toArray());
                    //添加凭证
                    HttpResponse addProjCredentialResp = HttpRequest.post(DOMAIN_HOST + "/ms/ticket/api/user/global/credentials/rel/" + credential + "/projects")
                            .cookie(getSessionCookie())
                            .contentType("application/json")
                            .body(JSONUtil.toJsonStr(postData))
                            .execute();
                    if (addProjCredentialResp.getStatus() == 200) {
                        String addResult = addProjCredentialResp.body();
                        log.info("凭证初始化:" + credential + "-----" + addResult);
                    }
                }
            }
        });
    }


    private boolean validateEnglishName(String projectName, String sessionCookie) {
        String checkUrl = DOMAIN_HOST + "/ms/projectmanager/api/user/project/cw/english_name/names/validate/?name=[projectName]&english_name=";
        HttpResponse response = HttpRequest.put(checkUrl.replace("[projectName]", projectName)).cookie(sessionCookie).execute();
        log.info("英文名称：{}，项目英文名称校验结果：{}", projectName, response.body());
        if (response.getStatus() == 200) {
            String result = response.body();
            if (!StrUtil.isEmpty(result)) {
                JSONObject json = JSONUtil.parseObj(result);
                if (json.getInt("code") == 0) {
                    return json.getBool("data");
                }
            }
        }
        return false;
    }

    private boolean validateProjectName(String projectName, String sessionCookie) {
        String checkUrl = DOMAIN_HOST + "/ms/projectmanager/api/user/project/cw/project_name/names/validate/?name=[projectName]&english_name=";
        HttpResponse response = HttpRequest.put(checkUrl.replace("[projectName]", projectName)).cookie(sessionCookie).execute();
        if (response.getStatus() == 200) {
            String result = response.body();
            if (!StrUtil.isEmpty(result)) {
                JSONObject json = JSONUtil.parseObj(result);
                log.info("项目名称校验结果：" + JSONUtil.toJsonStr(json));
                if (json.getInt("code") == 0) {
                    return json.getBool("data");
                }
            }
        }
        return false;
    }

    @Override
    public DevOpsProjectRespVO getExistProject(String projectName) {
        DevOpsProjectRespVO createResult = null;
        String existProjectUrl = DOMAIN_HOST + "/ms/projectmanager/api/user/project/cw/selectByType?name=" + projectName + "&status=USING&showTree=true&typeId=2";
        HttpResponse searchResp = HttpRequest.get(existProjectUrl).cookie(getSessionCookie()).execute();
        if (searchResp.getStatus() == 200) {
            String searchData = searchResp.body();
            log.info("查询Devops产品是否存在：" + searchData);
            if (!StrUtil.isEmpty(searchData)) {
                JSONObject json = JSONUtil.parseObj(searchData);
                if (json.getInt("status") == 0) {
                    //已存在，返回已存在的projectId
                    JSONArray ary = json.getJSONArray("data");
                    if (!ary.isEmpty()) {
                        JSONObject result = ary.getJSONObject(0);
                        createResult = new DevOpsProjectRespVO();
                        createResult.setProjectId(result.getStr("projectCode"));
                        createResult.setIsNew("N");
                        createResult.setProjectName(projectName);
                        createResult.setOriginData(JSONUtil.toJsonPrettyStr(result));
                        return createResult;
                    }
                }
            }
        }
        return createResult;
    }

    @Override
    public void updateMember4Project(UpdateMemberDTO updateMemberDTO) {
        log.info("updateMemberDTO is {}", JSONUtil.toJsonStr(updateMemberDTO));
        String businessDomain = updateMemberDTO.getBusinessDomain();
        String namespace = updateMemberDTO.getNamespace();
        Long systemId = updateMemberDTO.getSystemId();
        String incrUserCodes = updateMemberDTO.getIncrUserCodes();
        String delUserCodes = updateMemberDTO.getDelUserCodes();
        String incrUserNames = updateMemberDTO.getIncrUserNames();
        String delUserNames = updateMemberDTO.getDelUserNames();
        List<String> del = Arrays.stream(delUserNames.split(",")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(del)) {
            gitService.removeUserFromGitGroup(businessDomain, namespace, del);
        }
        List<String> add = Arrays.stream(incrUserNames.split(",")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(add)) {
            gitService.addUserToGitGroup(businessDomain, namespace, add, GitLabAccessLevel.OWNER);
        }
        List<String> delCodes = Arrays.stream(delUserCodes.split(",")).collect(Collectors.toList());
        List<String> incrCodes = Arrays.stream(incrUserCodes.split(",")).collect(Collectors.toList());
        updateProject(systemId, delCodes, incrCodes);
    }

    public String getProjectCode(Long applicationId) {
        ProjectConfigSaveReqVO reqVO = new ProjectConfigSaveReqVO();
        reqVO.setProjectId(applicationId);
        reqVO.setConfigKey(DEVOPS_BASIC_INFO_KEY);
        ProjectConfigDO projectConfigDO = projectConfigMapper.selectByProjectIdAndConfigKey(reqVO);
        if (projectConfigDO == null) {
            return null;
        }
        JSONObject proJson = JSONUtil.parseObj(projectConfigDO.getConfigContent());
        return proJson.getStr("projectCode");
    }

    private String getProjectName(Long applicationId) {
        ProjectConfigSaveReqVO reqVO = new ProjectConfigSaveReqVO();
        reqVO.setProjectId(applicationId);
        reqVO.setConfigKey(DEVOPS_BASIC_INFO_KEY);
        ProjectConfigDO projectConfigDO = projectConfigMapper.selectByProjectIdAndConfigKey(reqVO);
        JSONObject proJson = JSONUtil.parseObj(projectConfigDO.getConfigContent());
        return proJson.getStr("projectName");
    }

    private String getProjectEnglishNameCustom(Long applicationId) {
        ProjectConfigSaveReqVO reqVO = new ProjectConfigSaveReqVO();
        reqVO.setProjectId(applicationId);
        reqVO.setConfigKey(DEVOPS_BASIC_INFO_KEY);
        ProjectConfigDO projectConfigDO = projectConfigMapper.selectByProjectIdAndConfigKey(reqVO);
        JSONObject proJson = JSONUtil.parseObj(projectConfigDO.getConfigContent());
        return proJson.getStr("englishNameCustom");
    }
}

