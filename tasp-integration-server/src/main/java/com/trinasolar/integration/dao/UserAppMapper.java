package com.trinasolar.integration.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.integration.api.entity.UserApp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserAppMapper extends BaseMapper<UserApp> {

    List<UserApp> getUserAppByAppId(@Param("appId") Long appId);


    List<UserApp> selectUserByAppIds(@Param("appIds") List<Long> appIds);
}
