package com.trinasolar.integration.dao;

import com.trinasolar.integration.api.entity.UserApp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * UserAppMapper 测试类
 *
 * <AUTHOR>
 * @date 2025/9/10
 */
@SpringBootTest
class UserAppMapperTest {

    @Mock
    private UserAppMapper userAppMapper;

    /**
     * 测试正常情况：根据应用ID列表查询用户应用关系
     * 验证能够正确返回匹配的用户应用数据
     */
    @Test
    void selectUserByAppIds_WithValidAppIds_ShouldReturnUserApps() {
        // Given
        List<Long> appIds = Arrays.asList(1L, 1923581094534770690L);
        // When
        List<UserApp> result = userAppMapper.selectUserByAppIds(appIds);

        // Then
        assertNotNull(result);
     //   assertEquals(4, result.size());

        // 验证返回的数据包含正确的应用ID
        assertTrue(result.stream().anyMatch(ua -> ua.getAppId().equals(1L)));
        assertTrue(result.stream().anyMatch(ua -> ua.getAppId().equals(1923581094534770690L)));

        // 验证用户代码字段被正确填充
        assertTrue(result.stream().allMatch(ua -> ua.getUserCode() != null));

        verify(userAppMapper, times(1)).selectUserByAppIds(appIds);
    }

    /**
     * 测试正常情况：根据应用ID列表查询用户应用关系
     * 验证能够正确返回匹配的用户应用数据
     */
    @Test
    void getUserAppByAppId() {

        // When
        List<UserApp> result = userAppMapper.getUserAppByAppId(1L);

        // Then
        assertNotNull(result);

        // 验证返回的数据包含正确的应用ID
        assertTrue(result.stream().anyMatch(ua -> ua.getAppId().equals(1L)));

        // 验证用户代码字段被正确填充
        assertTrue(result.stream().allMatch(ua -> ua.getUserCode() != null));

    }

}
