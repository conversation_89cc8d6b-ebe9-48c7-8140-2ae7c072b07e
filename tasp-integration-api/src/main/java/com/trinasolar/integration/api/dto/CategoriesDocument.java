package com.trinasolar.integration.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName CategoriesDocument
 * @Description
 * <AUTHOR>
 * @Date 2025/5/14 15:46
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class CategoriesDocument implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotNull
    private Long documentId;

    private Long categoriesId;

    private String categoriesIds;

    private String tagIds;

    private Integer sort;

    private String docVersion;

    private Integer isRelease;

    private Integer isApprove;

    private String collectionId;
}
