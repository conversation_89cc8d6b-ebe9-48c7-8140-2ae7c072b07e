package com.trinasolar.integration.api.knowledge;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(name = "空间对象")
public class SpaceDTO {
    @Schema(name = "主键ID")
    private Long id;

    @Schema(name = "关联项目id")
    @NotNull(message = "关联项目ID不能为空")
    private Long projectId;

    @Schema(name = "权限类型，1-公开，2-私有")
    @NotNull(message = "权限类型不能为空")
    private Integer spaceType;

    @Schema(name = "名称")
    @NotBlank(message = "空间名称不能为空")
    private String name;

    @Schema(name = "桶名称")
    private String bucketName;

    @Schema(name = "备注")
    private String description;

}
