package com.trinasolar.integration.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendApiDTO {
    @Schema(description = "HTTP请求方法", example = "POST")
    private String method;

    @Schema(description = "目标API URL", example = "https://api.example.com/data")
    private String url;

    @Schema(description = "请求头信息")
    private Map<String, String> header;

    @Schema(description = "请求体内容")
    private Object body;

    @Schema(description = "上传文件映射，key为字段名，value为文件")
    private Map<String, MultipartFile> files;

    @Schema(description = "路径参数")
    private Map<String, String> pathParams;

    @Schema(description = "查询参数")
    private Map<String, String> queryParams;


}
