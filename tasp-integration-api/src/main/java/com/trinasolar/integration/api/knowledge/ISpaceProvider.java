package com.trinasolar.integration.api.knowledge;


import com.trinasolar.integration.api.dto.CategoriesDocument;
import com.trinasolar.integration.api.dto.CategoryDocmentDTO;
import com.trinasolar.integration.api.resq.R;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @describe
 * @author: zheng<PERSON><PERSON>
 * @create: 2021-12-02 15:47:54
 **/
@FeignClient(name = "tasp-knowledge",path = "/kepler/doc/", url = "${upms.url:https://tasp.trinasolar.com}")
public interface ISpaceProvider {

    @PostMapping("provider/space/create")
    @Schema(name ="创建空间", title = "创建空间")
    CommonResult<Long> create(@RequestBody SpaceDTO spaceDTO);

    @PostMapping("provider/space/createBatch")
    @Schema(name ="批量创建空间", title = "批量创建空间")
    CommonResult<Map<Long, String>> createBatch(@RequestBody List<SpaceDTO> dtoList);

    @PostMapping("provider/space/update/project")
    @Schema(name ="更新空间", title = "更新空间")
    CommonResult<Boolean> updateByProject(@RequestBody SpaceDTO spaceDTO);

    @PostMapping("provider/space/delete/project/{projectId}")
    @Schema(name ="删除空间", title = "删除空间")
    CommonResult<Long> deleteByProject(@PathVariable("projectId") Long projectId);

    /**
     * 创建新文档关联
     * @param categoriesDocument
     * @return
     */
    @PostMapping("global/categories/document")
    R<Boolean> add(@RequestBody CategoryDocmentDTO categoriesDocument);

    /**
     * 删除文档关联
     * @param categoriesDocument
     * @return
     */
    @PostMapping("global/categories/document/delete")
    R<Boolean> delete(@Valid @RequestBody CategoriesDocument categoriesDocument);

    /**
     * 获取知识分类目录
     * @return
     */
    @GetMapping("global/categories/getCatalog")
    R<Long> getCatalog();

}
